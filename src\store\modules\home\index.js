import { SetupStoreId } from "@/enum";
import { defineStore } from "pinia";
import { computed, reactive, toRefs } from "vue";
import apiAjax from "@/api/index";
export const useHome = defineStore(
  SetupStoreId.Home,
  () => {
    // states
    const HomeData = reactive({
      navList: {
        timingData: [
          { system: "UTC(NTSC)-BDT", value: 200 },
          { system: "UTC(NTSC)-GPST", value: 200 },
          { system: "UTC(NTSC)-GLNT", value: 200 },
          { system: "UTC(NTSC)-GST", value: 200 },
        ],
        alertsList: [],
      },
      // 方法
      sectionFn: {
        // 星地融合授时信息
        fusion: async () => {},
        // 卫星伪码授时增强信息
        pseudoRange: async () => {},
        // 授时精度
        accuracy: async () => {},
      },
      activeTab: "fusion",
    });
    // 定时器
    let times = null;
    // 设置定时器
    const setTimes = (info = true) => {
      if (info) {
        times = setInterval(() => {
          HomeData.sectionFn[HomeData.activeTab]();
        }, 6000);
      } else {
        clearInterval(times);
        times = null;
      }
    };
    // 初始化
    const init = async () => {
      await getAlertsList();
      HomeData.sectionFn[HomeData.activeTab]();
      setTimes();
    };

    const getAlertsList = async () => {
      let { data } = await apiAjax.get(
        "/api/jnx/fusion/apps/satellite/home/<USER>/errorInfo/findLatest",
      );
      HomeData.navList.alertsList = data.map((i) => {
        return {
          id: i.id,
          message:
            i.timestamp +
            " 来源(" +
            i.resourceInstanceCode +
            ") " +
            i.errorDesc,
        };
      });
    };

    const changactiveTab = async () => {
      console.log(HomeData.activeTab);
      setTimes(false);
      await HomeData.sectionFn[HomeData.activeTab]();
      setTimes();
    };

    const setChartfn = (name, fn) => {
      HomeData.sectionFn[name] = fn;
    };

    // getters

    // actions

    return {
      ...toRefs(HomeData),
      init,
      changactiveTab,
      setChartfn,
    };
  },
  {
    // 是否持久化到浏览器缓存localStorage
    persist: false,
  },
);
