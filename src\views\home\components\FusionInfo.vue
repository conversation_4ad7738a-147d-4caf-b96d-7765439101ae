<template>
  <div class="fusion-info">
    <div class="chart-content">
      <line-chart :data="lineChartOption" ref="fusionChartRef" />
    </div>
    <div class="tables-section">
      <div class="table-wrapper">
        <div class="table-title">星地融合授时偏差</div>
        <el-table :data="timeDiffList" border style="width: 100%" height="250">
          <el-table-column prop="timestamp" label="UTC-时间" width="180" />
          <el-table-column prop="BDS" label="UTC-BDS (ns)" />
          <el-table-column prop="GPS" label="UTC-GPS (ns)" />
          <el-table-column prop="GLONASS" label="UTC-GLONASS (ns)" />
          <el-table-column prop="GALILEO" label="UTC-GALILEO (ns)" />
        </el-table>
      </div>
      <div class="table-wrapper">
        <div class="table-title">星地融合授时偏差模型参数</div>
        <el-table
          :data="modelParamsList"
          border
          style="width: 100%"
          height="250"
        >
          <el-table-column
            prop="generateTime"
            label="参数成时间(UTC)"
            width="180"
          />
          <el-table-column prop="systemType" label="时差类型" />
          <el-table-column prop="timestamp" label="TOC" width="180" />
          <el-table-column prop="a0" label="A0(1E-10s)" />
          <el-table-column prop="a1" label="A1(1E-16s/s)" />
          <el-table-column prop="a2" label="A2(1E-21 s/s²)" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import LineChart from "@/components/lineChart/lineChart.vue";
import { useHome } from "@/store/modules/home";
import apiAjax from "@/api/index";
const HomeData = useHome();
// 生成图表数据
const lineChartOption = ref({
  xAxis: {
    type: "category",
    data: [],
  },
  yAxis: {
    type: "value",
    name: "时差(ns)",
    scale: true, // 自动缩放
    min: function (value) {
      return Math.floor(value.min - 5); // 最小值向下取整并留10%余量
    },
    max: function (value) {
      return Math.ceil(value.max + 5); // 最大值向上取整并留10%余量
    },
  },
  legend: {
    bottom: 10,
    data: [],
  },
  grid: {
    top: "30px",
    left: "50px",
    right: "30px",
    bottom: "60px",
  },
  tooltip: {
    trigger: "axis",
    formatter: function (params) {
      const timeLabel = params[0].name;
      let result = `${timeLabel}<br/>`;
      params.forEach((param) => {
        if (param.value !== null && param.value !== undefined) {
          result += `${param.seriesName}: ${param.value} ns<br/>`;
        }
      });
      return result;
    },
  },
  series: [],
});

// 生成星地融合授时偏差数据
const timeDiffList = ref([]);
const fusionChartRef = ref();
// 生成星地融合授时偏差模型参数数据
const modelParamsList = ref([]);

const satelliteCodingType = {
  "01": "BDS",
  "02": "GPS",
  "03": "GLONASS",
  "04": "GALILEO",
};

// 生成时间轴数据（按分钟）
const generateTimeAxis = (startTime, endTime) => {
  const start = new Date(startTime);
  const end = new Date(endTime);
  const timeAxis = [];

  const current = new Date(start);
  while (current <= end) {
    timeAxis.push(
      current
        .toLocaleString("zh-CN", {
          // month: "2-digit",
          // day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        })
        .replace(/\//g, "-"),
    );
    current.setMinutes(current.getMinutes() + 1);
  }

  return timeAxis;
};

// 处理图表数据
const processChartData = (chartValues) => {
  const series = [];
  const legendData = [];

  Object.keys(chartValues).forEach((systemCode) => {
    const systemName = satelliteCodingType[systemCode];
    const frequencies = chartValues[systemCode];

    Object.keys(frequencies).forEach((frequencyId) => {
      const seriesName = `${systemName}(${frequencyId})`;
      legendData.push(seriesName);

      series.push({
        name: seriesName,
        type: "line",
        data: frequencies[frequencyId],
        connectNulls: false, // 不连接空值
        symbol: "circle",
        symbolSize: 4,
        lineStyle: {
          width: 2,
        },
      });
    });
  });

  return { series, legendData };
};

// 处理星地融合授时偏差表格数据
const processTimeDiffData = (predictValues) => {
  // 按时间戳分组
  const groupedData = {};

  predictValues.forEach((item) => {
    const timestamp = item.timestamp;
    if (!groupedData[timestamp]) {
      groupedData[timestamp] = { timestamp };
    }

    const systemName = satelliteCodingType[item.navigationSystemCode];
    if (systemName) {
      groupedData[timestamp][systemName] = item.frequencyBias;
    }
  });

  return Object.values(groupedData);
};

// 处理模型参数表格数据
const processModelParamsData = (modelParams) => {
  return modelParams.map((item) => ({
    ...item,
    systemType: `${satelliteCodingType[item.navigationSystemCode]}-${item.frequencyId}`,
  }));
};

const getfusionData = async () => {
  try {
    let { data } = await apiAjax.get(
      "/api/jnx/fusion/apps/satellite/home/<USER>/timeDiff/xtwmsspc/getLatest",
    );
    if (data) {
      // 生成时间轴
      const timeAxis = generateTimeAxis(data.startTime, data.endTime);

      // 处理图表数据
      const { series, legendData } = processChartData(data.chartValues);

      // 更新图表配置
      lineChartOption.value = {
        ...lineChartOption.value,
        xAxis: {
          ...lineChartOption.value.xAxis,
          data: timeAxis,
        },
        legend: {
          ...lineChartOption.value.legend,
          data: legendData,
        },
        series: series,
      };

      // 处理表格数据
      timeDiffList.value = processTimeDiffData(data.predictValues || []);
      modelParamsList.value = processModelParamsData(data.modelParams || []);

      // 更新图表
      if (fusionChartRef.value) {
        fusionChartRef.value.setInfos();
        fusionChartRef.value.getIns().setOption(lineChartOption.value);
      }
    }
  } catch (error) {
    console.error("获取融合数据失败:", error);
  }
};
onMounted(() => {
  HomeData.setChartfn("fusion", getfusionData);
});
</script>

<style lang="scss" scoped>
.fusion-info {
  .chart-content {
    height: 420px;
    margin-bottom: 20px;
  }

  .tables-section {
    display: flex;
    gap: 20px;
    margin-top: 20px;

    .table-wrapper {
      flex: 1;
      background-color: #fff;
      border-radius: 4px;
      padding: 15px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .table-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #303133;
        padding-left: 5px;
        border-left: 4px solid #409eff;
      }
    }
  }
}
</style>
